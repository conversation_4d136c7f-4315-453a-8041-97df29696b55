<template>
  <div class="account-query-component">
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>账务查询</h1>
    </div>

    <!-- 查询类型选择 -->
    <div v-if="!selectedQueryType" class="query-type-selection">
      <h2>请选择查询类型</h2>
      <div class="query-type-cards">
        <div class="query-card" @click="selectQueryType('balance')">
          <div class="card-icon">
            <el-icon size="48">
              <Document />
            </el-icon>
          </div>
          <h3>科目余额表查询</h3>
          <p>查看各科目的期初余额、本期发生额和期末余额</p>
          <ul class="card-features">
            <li>显示完整的科目余额数据表格</li>
            <li>一键查询，无需复杂条件设置</li>
            <li>自动计算各列汇总统计</li>
          </ul>
        </div>

        <div class="query-card" @click="selectQueryType('detail')">
          <div class="card-icon">
            <el-icon size="48">
              <Search />
            </el-icon>
          </div>
          <h3>明细账查询</h3>
          <p>查询指定年月的明细账数据</p>
          <ul class="card-features">
            <li>按年月条件查询明细账</li>
            <li>支持灵活的时间范围选择</li>
            <li>详细的账务记录展示</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 明细账查询参数设置 -->
    <div v-else-if="selectedQueryType === 'detail'" class="detail-query-params">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>明细账查询参数</span>
            <el-button type="text" @click="resetQueryType">
              <el-icon><ArrowLeft /></el-icon>
              重新选择
            </el-button>
          </div>
        </template>
        
        <el-form :model="detailQueryForm" label-width="100px" class="query-form">
          <el-form-item label="查询年份" required>
            <el-date-picker
              v-model="detailQueryForm.year"
              type="year"
              placeholder="选择年份"
              format="YYYY"
              value-format="YYYY"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="查询月份" required>
            <el-select v-model="detailQueryForm.month" placeholder="选择月份" style="width: 200px">
              <el-option
                v-for="month in months"
                :key="month.value"
                :label="month.label"
                :value="month.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              :icon="Search" 
              @click="executeDetailQuery"
              :loading="loading"
            >
              查询明细账
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 科目余额表查询 -->
    <div v-else-if="selectedQueryType === 'balance'" class="balance-query-params">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>科目余额表查询</span>
            <el-button type="text" @click="resetQueryType">
              <el-icon><ArrowLeft /></el-icon>
              重新选择
            </el-button>
          </div>
        </template>
        
        <div class="balance-description">
          <p>点击下方按钮即可查询所有科目的余额数据，包括期初余额、本期发生额和期末余额。</p>
        </div>
        
        <el-button 
          type="primary" 
          :icon="Search" 
          @click="executeBalanceQuery"
          :loading="loading"
          size="large"
        >
          查询科目余额表
        </el-button>
      </el-card>
    </div>

    <!-- 查询结果展示 -->
    <div v-if="hasResults" class="result-panel">
      <div class="result-header">
        <h3>{{ selectedQueryType === 'balance' ? '科目余额表查询结果' : '明细账查询结果' }}</h3>
        <span class="result-count">共找到 {{ resultCount }} 条记录</span>
      </div>

      <NativeTableComponent
        :data="tableData"
        :width="1200"
        :height="500"
        class="query-table"
      />
    </div>
    
    <div v-else-if="!loading && hasQueried" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search, Document } from '@element-plus/icons-vue'
import NativeTableComponent from '../components/NativeTableComponent.vue'

const emit = defineEmits(['back'])

// 响应式数据
const selectedQueryType = ref('')
const loading = ref(false)
const hasQueried = ref(false)
const hasResults = ref(false)
const resultCount = ref(0)
const tableData = ref([])

// 明细账查询表单
const detailQueryForm = reactive({
  year: '',
  month: ''
})

// 月份选项
const months = [
  { label: '1月', value: '01' },
  { label: '2月', value: '02' },
  { label: '3月', value: '03' },
  { label: '4月', value: '04' },
  { label: '5月', value: '05' },
  { label: '6月', value: '06' },
  { label: '7月', value: '07' },
  { label: '8月', value: '08' },
  { label: '9月', value: '09' },
  { label: '10月', value: '10' },
  { label: '11月', value: '11' },
  { label: '12月', value: '12' }
]

function handleBack() {
  emit('back')
}

function selectQueryType(type) {
  selectedQueryType.value = type
  // 清空之前的查询结果
  clearResults()
}

function resetQueryType() {
  selectedQueryType.value = ''
  clearResults()
  // 重置明细账查询表单
  detailQueryForm.year = ''
  detailQueryForm.month = ''
}

function clearResults() {
  hasResults.value = false
  hasQueried.value = false
  tableData.value = []
  resultCount.value = 0
}

// 执行科目余额表查询
async function executeBalanceQuery() {
  loading.value = true
  try {
    const response = await fetch('http://localhost:8000/api/balance-sheet-query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ queryType: 'balance' })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.success && result.data && result.data.length > 0) {
      const headers = result.data[0]
      const rows = result.data.slice(1)
      
      // 将表格数据转换为NativeTableComponent需要的格式
      const tableDataFormatted = [headers, ...rows]
      
      tableData.value = tableDataFormatted
      resultCount.value = rows.length
      hasResults.value = true
      
      ElMessage.success(`科目余额表查询完成，共找到 ${rows.length} 条记录`)
    } else {
      throw new Error(result.message || '查询返回空结果')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
    hasQueried.value = true
  }
}

// 执行明细账查询
async function executeDetailQuery() {
  // 验证参数
  if (!detailQueryForm.year || !detailQueryForm.month) {
    ElMessage.warning('请选择查询年份和月份')
    return
  }
  
  loading.value = true
  try {
    const response = await fetch('http://localhost:8000/api/detail-ledger-query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        queryType: 'detail',
        year: detailQueryForm.year,
        month: detailQueryForm.month
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.success && result.data && result.data.length > 0) {
      const headers = result.data[0]
      const rows = result.data.slice(1)
      
      // 将表格数据转换为NativeTableComponent需要的格式
      const tableDataFormatted = [headers, ...rows]
      
      tableData.value = tableDataFormatted
      resultCount.value = rows.length
      hasResults.value = true
      
      ElMessage.success(`明细账查询完成，共找到 ${rows.length} 条记录`)
    } else {
      throw new Error(result.message || '查询返回空结果')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
    hasQueried.value = true
  }
}
</script>

<style scoped>
.account-query-component {
  width: 100%;
  padding: 20px;
  background-color: #f9fafb;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.header h1 {
  margin: 0 0 0 10px;
  font-size: 28px;
  color: #333;
}

/* 查询类型选择样式 */
.query-type-selection {
  text-align: center;
  margin-bottom: 30px;
}

.query-type-selection h2 {
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.query-type-cards {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.query-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 350px;
}

.query-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
  margin-bottom: 20px;
  color: #409eff;
}

.query-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.query-card p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.card-features li {
  padding: 6px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
  font-size: 14px;
}

.card-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #52c41a;
  font-weight: bold;
}

/* 参数设置样式 */
.detail-query-params,
.balance-query-params {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.query-form {
  max-width: 500px;
}

.balance-description {
  margin-bottom: 20px;
}

.balance-description p {
  color: #666;
  line-height: 1.6;
}

/* 结果展示样式 */
.result-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.result-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.result-count {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.query-table {
  border-radius: 8px;
  overflow: hidden;
}
</style>
